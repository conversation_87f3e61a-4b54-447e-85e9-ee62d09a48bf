* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* Flexair Brand Colors */
:root {
  --flexair-primary: #1e40af;
  --flexair-primary-light: #3b82f6;
  --flexair-accent: #0ea5e9;
  --flexair-gray-50: #f8fafc;
  --flexair-gray-100: #f1f5f9;
  --flexair-gray-200: #e2e8f0;
  --flexair-gray-300: #cbd5e1;
  --flexair-gray-400: #94a3b8;
  --flexair-gray-500: #64748b;
  --flexair-gray-600: #475569;
  --flexair-gray-700: #334155;
  --flexair-gray-800: #1e293b;
  --flexair-gray-900: #0f172a;
}

/* Smooth transitions for interactive elements */
button, a, input, select {
  transition: all 0.2s ease-in-out;
}

/* Focus styles */
button:focus, input:focus, select:focus {
  outline: 2px solid var(--flexair-primary);
  outline-offset: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--flexair-gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--flexair-gray-300);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--flexair-gray-400);
}
