import { Routes, Route, Navigate } from 'react-router-dom'
import { useState } from 'react'

// Flexair brand colors
const colors = {
  primary: '#1e40af', // Flexair blue
  primaryLight: '#3b82f6',
  secondary: '#64748b',
  accent: '#0ea5e9',
  gray: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a'
  }
}

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [userRole, setUserRole] = useState('manager')

  if (!isAuthenticated) {
    return (
      <div style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
        padding: '1rem'
      }}>
        <div style={{
          maxWidth: '420px',
          width: '100%',
          padding: '2.5rem',
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }}>
          {/* Logo */}
          <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
            <div style={{
              width: '80px',
              height: '80px',
              backgroundColor: colors.primary,
              borderRadius: '50%',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              margin: '0 auto 1rem',
              color: 'white',
              fontSize: '2rem',
              fontWeight: 'bold'
            }}>
              F
            </div>
            <h1 style={{
              fontSize: '1.5rem',
              fontWeight: 'bold',
              color: colors.gray[800],
              margin: 0
            }}>
              Flexair CRM
            </h1>
            <p style={{
              color: colors.gray[500],
              fontSize: '0.875rem',
              margin: '0.5rem 0 0 0'
            }}>
              Industrial Solutions Management
            </p>
          </div>

          <form onSubmit={(e) => {
            e.preventDefault()
            setIsAuthenticated(true)
          }}>
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.gray[700],
                marginBottom: '0.5rem'
              }}>
                Email Address
              </label>
              <input
                type="email"
                placeholder="Enter your email"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `2px solid ${colors.gray[200]}`,
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'border-color 0.2s',
                  outline: 'none'
                }}
                onFocus={(e) => e.target.style.borderColor = colors.primary}
                onBlur={(e) => e.target.style.borderColor = colors.gray[200]}
                required
              />
            </div>
            <div style={{ marginBottom: '1.5rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.gray[700],
                marginBottom: '0.5rem'
              }}>
                Password
              </label>
              <input
                type="password"
                placeholder="Enter your password"
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `2px solid ${colors.gray[200]}`,
                  borderRadius: '8px',
                  fontSize: '1rem',
                  transition: 'border-color 0.2s',
                  outline: 'none'
                }}
                onFocus={(e) => e.target.style.borderColor = colors.primary}
                onBlur={(e) => e.target.style.borderColor = colors.gray[200]}
                required
              />
            </div>
            <div style={{ marginBottom: '2rem' }}>
              <label style={{
                display: 'block',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.gray[700],
                marginBottom: '0.5rem'
              }}>
                Role
              </label>
              <select
                value={userRole}
                onChange={(e) => setUserRole(e.target.value)}
                style={{
                  width: '100%',
                  padding: '0.75rem',
                  border: `2px solid ${colors.gray[200]}`,
                  borderRadius: '8px',
                  fontSize: '1rem',
                  backgroundColor: 'white',
                  outline: 'none'
                }}
              >
                <option value="manager">Manager</option>
                <option value="sales_agent">Sales Agent</option>
                <option value="sales_assistant">Sales Assistant</option>
                <option value="technician">Technician</option>
              </select>
            </div>
            <button
              type="submit"
              style={{
                width: '100%',
                padding: '0.875rem',
                background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'transform 0.2s',
                boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
              }}
              onMouseOver={(e) => e.target.style.transform = 'translateY(-1px)'}
              onMouseOut={(e) => e.target.style.transform = 'translateY(0)'}
            >
              Sign In
            </button>
          </form>
          <div style={{
            textAlign: 'center',
            marginTop: '1.5rem',
            padding: '1rem',
            backgroundColor: colors.gray[50],
            borderRadius: '8px'
          }}>
            <p style={{
              fontSize: '0.75rem',
              color: colors.gray[500],
              margin: 0
            }}>
              Demo Environment - Use any credentials
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: colors.gray[50] }}>
      {/* Sidebar */}
      <div style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '280px',
        height: '100vh',
        background: `linear-gradient(180deg, ${colors.primary} 0%, ${colors.gray[800]} 100%)`,
        boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        zIndex: 50
      }}>
        {/* Logo Header */}
        <div style={{
          height: '80px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderBottom: `1px solid ${colors.gray[600]}`,
          padding: '0 1.5rem'
        }}>
          <div style={{
            width: '40px',
            height: '40px',
            backgroundColor: 'white',
            borderRadius: '8px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginRight: '0.75rem',
            color: colors.primary,
            fontSize: '1.25rem',
            fontWeight: 'bold'
          }}>
            F
          </div>
          <div>
            <h1 style={{
              fontSize: '1.25rem',
              fontWeight: 'bold',
              color: 'white',
              margin: 0
            }}>
              Flexair CRM
            </h1>
            <p style={{
              fontSize: '0.75rem',
              color: colors.gray[300],
              margin: 0
            }}>
              Industrial Solutions
            </p>
          </div>
        </div>

        {/* Navigation */}
        <nav style={{ padding: '1.5rem 1rem' }}>
          <div style={{ marginBottom: '1.5rem' }}>
            <p style={{
              fontSize: '0.75rem',
              fontWeight: '600',
              color: colors.gray[400],
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              marginBottom: '0.75rem'
            }}>
              Main Menu
            </p>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <NavItem icon="📊" label="Dashboard" active />
              <NavItem icon="🏭" label="Customers" />
              <NavItem icon="🎯" label="Leads" />
              <NavItem icon="📋" label="Quotes" />
              <NavItem icon="🔧" label="Service Requests" />
            </ul>
          </div>

          <div style={{ marginBottom: '1.5rem' }}>
            <p style={{
              fontSize: '0.75rem',
              fontWeight: '600',
              color: colors.gray[400],
              textTransform: 'uppercase',
              letterSpacing: '0.05em',
              marginBottom: '0.75rem'
            }}>
              Management
            </p>
            <ul style={{ listStyle: 'none', padding: 0, margin: 0 }}>
              <NavItem icon="📦" label="Products" />
              <NavItem icon="👥" label="Team" />
              <NavItem icon="📊" label="Reports" />
              <NavItem icon="⚙️" label="Settings" />
            </ul>
          </div>
        </nav>

        {/* User Profile */}
        <div style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          padding: '1rem',
          borderTop: `1px solid ${colors.gray[600]}`
        }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <div style={{
                width: '40px',
                height: '40px',
                borderRadius: '50%',
                background: `linear-gradient(135deg, ${colors.accent} 0%, ${colors.primaryLight} 100%)`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: 'white',
                fontSize: '0.875rem',
                fontWeight: '600'
              }}>
                JD
              </div>
              <div style={{ marginLeft: '0.75rem' }}>
                <p style={{
                  fontSize: '0.875rem',
                  fontWeight: '500',
                  color: 'white',
                  margin: 0
                }}>
                  John Doe
                </p>
                <p style={{
                  fontSize: '0.75rem',
                  color: colors.gray[300],
                  margin: 0,
                  textTransform: 'capitalize'
                }}>
                  {userRole.replace('_', ' ')}
                </p>
              </div>
            </div>
            <button
              onClick={() => setIsAuthenticated(false)}
              style={{
                background: 'none',
                border: 'none',
                color: colors.gray[300],
                cursor: 'pointer',
                fontSize: '1.25rem',
                padding: '0.5rem',
                borderRadius: '6px',
                transition: 'background-color 0.2s'
              }}
              onMouseOver={(e) => e.target.style.backgroundColor = colors.gray[700]}
              onMouseOut={(e) => e.target.style.backgroundColor = 'transparent'}
              title="Sign Out"
            >
              🚪
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div style={{ marginLeft: '280px', minHeight: '100vh' }}>
        {/* Top Header */}
        <header style={{
          height: '80px',
          backgroundColor: 'white',
          borderBottom: `1px solid ${colors.gray[200]}`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 2rem',
          boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
        }}>
          <div>
            <h2 style={{
              fontSize: '1.5rem',
              fontWeight: '600',
              color: colors.gray[800],
              margin: 0
            }}>
              Dashboard
            </h2>
            <p style={{
              fontSize: '0.875rem',
              color: colors.gray[500],
              margin: 0
            }}>
              Welcome back! Here's your business overview.
            </p>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '1rem' }}>
            <button style={{
              padding: '0.5rem',
              backgroundColor: colors.gray[100],
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1.25rem'
            }}>
              🔔
            </button>
            <button style={{
              padding: '0.5rem',
              backgroundColor: colors.gray[100],
              border: 'none',
              borderRadius: '8px',
              cursor: 'pointer',
              fontSize: '1.25rem'
            }}>
              ⚙️
            </button>
          </div>
        </header>

        {/* Page Content */}
        <main style={{ padding: '2rem' }}>
          <Routes>
            <Route path="/" element={<Navigate to="/dashboard" replace />} />
            <Route path="/dashboard" element={<DashboardPage userRole={userRole} />} />
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </main>
      </div>
    </div>
  )
}

// Navigation Item Component
function NavItem({ icon, label, active = false }) {
  return (
    <li style={{ marginBottom: '0.25rem' }}>
      <a href="#" style={{
        display: 'flex',
        alignItems: 'center',
        padding: '0.75rem 1rem',
        textDecoration: 'none',
        color: active ? 'white' : colors.gray[300],
        backgroundColor: active ? 'rgba(255, 255, 255, 0.1)' : 'transparent',
        borderRadius: '8px',
        fontSize: '0.875rem',
        fontWeight: active ? '600' : '500',
        transition: 'all 0.2s',
        border: active ? `1px solid rgba(255, 255, 255, 0.2)` : '1px solid transparent'
      }}
      onMouseOver={(e) => {
        if (!active) {
          e.target.style.backgroundColor = 'rgba(255, 255, 255, 0.05)'
          e.target.style.color = 'white'
        }
      }}
      onMouseOut={(e) => {
        if (!active) {
          e.target.style.backgroundColor = 'transparent'
          e.target.style.color = colors.gray[300]
        }
      }}
      >
        <span style={{ marginRight: '0.75rem', fontSize: '1rem' }}>{icon}</span>
        {label}
      </a>
    </li>
  )
}

function DashboardPage({ userRole }) {
  // Role-specific stats
  const getStatsForRole = (role) => {
    const baseStats = [
      { name: 'Industrial Clients', value: '847', change: '+12%', icon: '🏭', color: colors.primary },
      { name: 'Active Projects', value: '156', change: '+8%', icon: '🔧', color: colors.accent },
      { name: 'Monthly Revenue', value: '₱2.4M', change: '+15%', icon: '💰', color: '#10b981' },
      { name: 'Service Requests', value: '89', change: '+5%', icon: '📋', color: '#f59e0b' }
    ]

    if (role === 'sales_agent' || role === 'sales_assistant') {
      return [
        { name: 'My Leads', value: '23', change: '+18%', icon: '🎯', color: colors.primary },
        { name: 'Quotes Sent', value: '12', change: '+25%', icon: '📄', color: colors.accent },
        { name: 'Deals Closed', value: '8', change: '+33%', icon: '✅', color: '#10b981' },
        { name: 'Pipeline Value', value: '₱890K', change: '+22%', icon: '💼', color: '#f59e0b' }
      ]
    }

    if (role === 'technician') {
      return [
        { name: 'Assigned Tasks', value: '15', change: '+10%', icon: '🔧', color: colors.primary },
        { name: 'Completed Today', value: '7', change: '+40%', icon: '✅', color: '#10b981' },
        { name: 'Pending Repairs', value: '8', change: '-12%', icon: '⚠️', color: '#ef4444' },
        { name: 'Client Rating', value: '4.8', change: '+2%', icon: '⭐', color: '#f59e0b' }
      ]
    }

    return baseStats
  }

  const stats = getStatsForRole(userRole)

  return (
    <div>
      {/* Welcome Section */}
      <div style={{
        background: `linear-gradient(135deg, ${colors.primary} 0%, ${colors.primaryLight} 100%)`,
        borderRadius: '16px',
        padding: '2rem',
        marginBottom: '2rem',
        color: 'white'
      }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div>
            <h1 style={{
              fontSize: '2rem',
              fontWeight: 'bold',
              margin: '0 0 0.5rem 0'
            }}>
              Good morning, John! 👋
            </h1>
            <p style={{
              fontSize: '1.125rem',
              opacity: 0.9,
              margin: 0
            }}>
              {userRole === 'manager' && "Here's your business overview for today"}
              {userRole === 'sales_agent' && "Ready to close some deals today?"}
              {userRole === 'sales_assistant' && "Let's support our sales team!"}
              {userRole === 'technician' && "Your service schedule is ready"}
            </p>
          </div>
          <div style={{
            width: '80px',
            height: '80px',
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '2rem'
          }}>
            {userRole === 'manager' && '📊'}
            {userRole === 'sales_agent' && '🎯'}
            {userRole === 'sales_assistant' && '📞'}
            {userRole === 'technician' && '🔧'}
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        {stats.map((stat, index) => (
          <div key={index} style={{
            backgroundColor: 'white',
            padding: '1.5rem',
            borderRadius: '12px',
            boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
            border: `1px solid ${colors.gray[200]}`,
            transition: 'transform 0.2s, box-shadow 0.2s',
            cursor: 'pointer'
          }}
          onMouseOver={(e) => {
            e.currentTarget.style.transform = 'translateY(-2px)'
            e.currentTarget.style.boxShadow = '0 8px 15px -3px rgba(0, 0, 0, 0.1)'
          }}
          onMouseOut={(e) => {
            e.currentTarget.style.transform = 'translateY(0)'
            e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1)'
          }}
          >
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div>
                <p style={{
                  fontSize: '0.875rem',
                  color: colors.gray[500],
                  margin: '0 0 0.5rem 0',
                  fontWeight: '500'
                }}>
                  {stat.name}
                </p>
                <p style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: colors.gray[800],
                  margin: '0 0 0.25rem 0'
                }}>
                  {stat.value}
                </p>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <span style={{
                    fontSize: '0.875rem',
                    color: stat.change.startsWith('+') ? '#10b981' : '#ef4444',
                    fontWeight: '600'
                  }}>
                    {stat.change}
                  </span>
                  <span style={{
                    fontSize: '0.75rem',
                    color: colors.gray[500],
                    marginLeft: '0.5rem'
                  }}>
                    vs last month
                  </span>
                </div>
              </div>
              <div style={{
                width: '60px',
                height: '60px',
                backgroundColor: `${stat.color}15`,
                borderRadius: '12px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '1.5rem'
              }}>
                {stat.icon}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Content Grid */}
      <div style={{
        display: 'grid',
        gridTemplateColumns: '2fr 1fr',
        gap: '1.5rem',
        marginBottom: '2rem'
      }}>
        {/* Recent Activity */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${colors.gray[200]}`
        }}>
          <div style={{
            padding: '1.5rem 1.5rem 0 1.5rem',
            borderBottom: `1px solid ${colors.gray[100]}`
          }}>
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: colors.gray[800],
              margin: '0 0 1rem 0'
            }}>
              Recent Activity
            </h3>
          </div>
          <div style={{ padding: '1.5rem' }}>
            {[
              {
                icon: '🔧',
                text: 'Maintenance completed for Manila Steel Corp vacuum system',
                time: '2 hours ago',
                type: 'service'
              },
              {
                icon: '💰',
                text: 'Quote #Q-2024-156 approved by Cebu Manufacturing Inc.',
                time: '4 hours ago',
                type: 'sales'
              },
              {
                icon: '📦',
                text: 'New industrial vacuum cleaner added to inventory',
                time: '6 hours ago',
                type: 'inventory'
              },
              {
                icon: '👥',
                text: 'New client onboarded: Davao Industrial Solutions',
                time: '1 day ago',
                type: 'client'
              }
            ].map((activity, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'flex-start',
                marginBottom: index < 3 ? '1rem' : 0
              }}>
                <div style={{
                  width: '40px',
                  height: '40px',
                  backgroundColor: colors.gray[100],
                  borderRadius: '50%',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  fontSize: '1rem',
                  flexShrink: 0
                }}>
                  {activity.icon}
                </div>
                <div style={{ marginLeft: '1rem', flex: 1 }}>
                  <p style={{
                    fontSize: '0.875rem',
                    color: colors.gray[800],
                    margin: '0 0 0.25rem 0',
                    lineHeight: '1.4'
                  }}>
                    {activity.text}
                  </p>
                  <p style={{
                    fontSize: '0.75rem',
                    color: colors.gray[500],
                    margin: 0
                  }}>
                    {activity.time}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Actions */}
        <div style={{
          backgroundColor: 'white',
          borderRadius: '12px',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
          border: `1px solid ${colors.gray[200]}`
        }}>
          <div style={{
            padding: '1.5rem 1.5rem 0 1.5rem',
            borderBottom: `1px solid ${colors.gray[100]}`
          }}>
            <h3 style={{
              fontSize: '1.25rem',
              fontWeight: '600',
              color: colors.gray[800],
              margin: '0 0 1rem 0'
            }}>
              Quick Actions
            </h3>
          </div>
          <div style={{ padding: '1.5rem' }}>
            {[
              { icon: '➕', label: 'New Quote', color: colors.primary },
              { icon: '📞', label: 'Schedule Call', color: colors.accent },
              { icon: '🔧', label: 'Service Request', color: '#10b981' },
              { icon: '📊', label: 'View Reports', color: '#f59e0b' }
            ].map((action, index) => (
              <button key={index} style={{
                width: '100%',
                padding: '0.75rem',
                backgroundColor: colors.gray[50],
                border: `1px solid ${colors.gray[200]}`,
                borderRadius: '8px',
                marginBottom: index < 3 ? '0.75rem' : 0,
                cursor: 'pointer',
                transition: 'all 0.2s',
                display: 'flex',
                alignItems: 'center',
                fontSize: '0.875rem',
                fontWeight: '500',
                color: colors.gray[700]
              }}
              onMouseOver={(e) => {
                e.target.style.backgroundColor = action.color
                e.target.style.color = 'white'
                e.target.style.borderColor = action.color
              }}
              onMouseOut={(e) => {
                e.target.style.backgroundColor = colors.gray[50]
                e.target.style.color = colors.gray[700]
                e.target.style.borderColor = colors.gray[200]
              }}
              >
                <span style={{ marginRight: '0.75rem', fontSize: '1rem' }}>
                  {action.icon}
                </span>
                {action.label}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

export default App
