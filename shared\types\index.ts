// Shared TypeScript types for Flexair CRM

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'admin',
  MANAGER = 'manager',
  SALES_AGENT = 'sales_agent',
  SALES_ASSISTANT = 'sales_assistant',
  TECHNICIAN = 'technician'
}

export interface Customer {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: Address;
  industry: string;
  status: CustomerStatus;
  assignedTo: string; // User ID
  createdAt: Date;
  updatedAt: Date;
}

export enum CustomerStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PROSPECT = 'prospect',
  LEAD = 'lead'
}

export interface Contact {
  id: string;
  customerId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  position: string;
  isPrimary: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Lead {
  id: string;
  title: string;
  description: string;
  value: number;
  status: LeadStatus;
  priority: Priority;
  source: string;
  customerId?: string;
  assignedTo: string; // User ID
  expectedCloseDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum LeadStatus {
  NEW = 'new',
  CONTACTED = 'contacted',
  QUALIFIED = 'qualified',
  PROPOSAL = 'proposal',
  NEGOTIATION = 'negotiation',
  CLOSED_WON = 'closed_won',
  CLOSED_LOST = 'closed_lost'
}

export enum Priority {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  URGENT = 'urgent'
}

export interface Task {
  id: string;
  title: string;
  description: string;
  status: TaskStatus;
  priority: Priority;
  assignedTo: string; // User ID
  relatedTo?: {
    type: 'customer' | 'lead' | 'contact';
    id: string;
  };
  dueDate: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum TaskStatus {
  TODO = 'todo',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface CreateCustomerRequest {
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: Address;
  industry: string;
  assignedTo: string;
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {
  status?: CustomerStatus;
}

export interface CreateLeadRequest {
  title: string;
  description: string;
  value: number;
  priority: Priority;
  source: string;
  customerId?: string;
  expectedCloseDate: Date;
}

export interface UpdateLeadRequest extends Partial<CreateLeadRequest> {
  status?: LeadStatus;
}

export interface CreateTaskRequest {
  title: string;
  description: string;
  priority: Priority;
  assignedTo: string;
  relatedTo?: {
    type: 'customer' | 'lead' | 'contact';
    id: string;
  };
  dueDate: Date;
}

export interface UpdateTaskRequest extends Partial<CreateTaskRequest> {
  status?: TaskStatus;
}

// Flexair-specific types
export interface Product {
  id: string;
  name: string;
  category: ProductCategory;
  description: string;
  specifications: string;
  price: number;
  stockQuantity: number;
  supplier: string;
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ProductCategory {
  INDUSTRIAL_VACUUM = 'industrial_vacuum',
  PNEUMATIC_TOOLS = 'pneumatic_tools',
  AIR_COMPRESSORS = 'air_compressors',
  FILTRATION_SYSTEMS = 'filtration_systems',
  SPARE_PARTS = 'spare_parts'
}

export interface Quote {
  id: string;
  customerId: string;
  leadId?: string;
  quoteNumber: string;
  items: QuoteItem[];
  subtotal: number;
  tax: number;
  total: number;
  validUntil: Date;
  status: QuoteStatus;
  notes?: string;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface QuoteItem {
  id: string;
  productId: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  total: number;
}

export enum QuoteStatus {
  DRAFT = 'draft',
  SENT = 'sent',
  ACCEPTED = 'accepted',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export interface ServiceRequest {
  id: string;
  customerId: string;
  type: ServiceType;
  priority: Priority;
  description: string;
  status: ServiceStatus;
  assignedTo?: string;
  scheduledDate?: Date;
  completedDate?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ServiceType {
  INSTALLATION = 'installation',
  MAINTENANCE = 'maintenance',
  REPAIR = 'repair',
  TRAINING = 'training',
  CONSULTATION = 'consultation'
}

export enum ServiceStatus {
  PENDING = 'pending',
  SCHEDULED = 'scheduled',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  CANCELLED = 'cancelled'
}
