"use strict";
// Shared TypeScript types for Flexair CRM
Object.defineProperty(exports, "__esModule", { value: true });
exports.ServiceStatus = exports.ServiceType = exports.QuoteStatus = exports.ProductCategory = exports.TaskStatus = exports.Priority = exports.LeadStatus = exports.CustomerStatus = exports.UserRole = void 0;
var UserRole;
(function (UserRole) {
    UserRole["ADMIN"] = "admin";
    UserRole["MANAGER"] = "manager";
    UserRole["SALES_AGENT"] = "sales_agent";
    UserRole["SALES_ASSISTANT"] = "sales_assistant";
    UserRole["TECHNICIAN"] = "technician";
})(UserRole || (exports.UserRole = UserRole = {}));
var CustomerStatus;
(function (CustomerStatus) {
    CustomerStatus["ACTIVE"] = "active";
    CustomerStatus["INACTIVE"] = "inactive";
    CustomerStatus["PROSPECT"] = "prospect";
    CustomerStatus["LEAD"] = "lead";
})(CustomerStatus || (exports.CustomerStatus = CustomerStatus = {}));
var LeadStatus;
(function (LeadStatus) {
    LeadStatus["NEW"] = "new";
    LeadStatus["CONTACTED"] = "contacted";
    LeadStatus["QUALIFIED"] = "qualified";
    LeadStatus["PROPOSAL"] = "proposal";
    LeadStatus["NEGOTIATION"] = "negotiation";
    LeadStatus["CLOSED_WON"] = "closed_won";
    LeadStatus["CLOSED_LOST"] = "closed_lost";
})(LeadStatus || (exports.LeadStatus = LeadStatus = {}));
var Priority;
(function (Priority) {
    Priority["LOW"] = "low";
    Priority["MEDIUM"] = "medium";
    Priority["HIGH"] = "high";
    Priority["URGENT"] = "urgent";
})(Priority || (exports.Priority = Priority = {}));
var TaskStatus;
(function (TaskStatus) {
    TaskStatus["TODO"] = "todo";
    TaskStatus["IN_PROGRESS"] = "in_progress";
    TaskStatus["COMPLETED"] = "completed";
    TaskStatus["CANCELLED"] = "cancelled";
})(TaskStatus || (exports.TaskStatus = TaskStatus = {}));
var ProductCategory;
(function (ProductCategory) {
    ProductCategory["INDUSTRIAL_VACUUM"] = "industrial_vacuum";
    ProductCategory["PNEUMATIC_TOOLS"] = "pneumatic_tools";
    ProductCategory["AIR_COMPRESSORS"] = "air_compressors";
    ProductCategory["FILTRATION_SYSTEMS"] = "filtration_systems";
    ProductCategory["SPARE_PARTS"] = "spare_parts";
})(ProductCategory || (exports.ProductCategory = ProductCategory = {}));
var QuoteStatus;
(function (QuoteStatus) {
    QuoteStatus["DRAFT"] = "draft";
    QuoteStatus["SENT"] = "sent";
    QuoteStatus["ACCEPTED"] = "accepted";
    QuoteStatus["REJECTED"] = "rejected";
    QuoteStatus["EXPIRED"] = "expired";
})(QuoteStatus || (exports.QuoteStatus = QuoteStatus = {}));
var ServiceType;
(function (ServiceType) {
    ServiceType["INSTALLATION"] = "installation";
    ServiceType["MAINTENANCE"] = "maintenance";
    ServiceType["REPAIR"] = "repair";
    ServiceType["TRAINING"] = "training";
    ServiceType["CONSULTATION"] = "consultation";
})(ServiceType || (exports.ServiceType = ServiceType = {}));
var ServiceStatus;
(function (ServiceStatus) {
    ServiceStatus["PENDING"] = "pending";
    ServiceStatus["SCHEDULED"] = "scheduled";
    ServiceStatus["IN_PROGRESS"] = "in_progress";
    ServiceStatus["COMPLETED"] = "completed";
    ServiceStatus["CANCELLED"] = "cancelled";
})(ServiceStatus || (exports.ServiceStatus = ServiceStatus = {}));
//# sourceMappingURL=index.js.map